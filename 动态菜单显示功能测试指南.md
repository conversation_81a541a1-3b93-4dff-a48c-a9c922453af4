# GPSLogger 动态菜单显示功能测试指南

## 功能概述

本次实现了动态菜单显示功能，只有在设置中开启了自动发送的目标服务才会在侧边栏和主界面的发送菜单中显示。未开启的服务将不会显示在菜单中。

## 实现的功能

### 1. 侧边栏动态显示
- 只显示已启用自动发送的服务项
- 支持的服务包括：
  - 自定义URL
  - Dropbox
  - Google云端硬盘
  - SFTP
  - OpenGTS
  - OpenStreetMap
  - 电子邮件
  - 私有云存储
  - FTP

### 2. 主界面菜单动态显示
- 上传菜单中只显示已启用自动发送的服务选项
- 未启用的服务选项将被隐藏

### 3. 实时更新
- 当从设置界面返回时，菜单会自动刷新
- 设置改变后立即生效

## 测试步骤

### 测试前准备
1. 安装新构建的APK：`gpslogger-debug.apk`
2. 确保手机已连接并启用USB调试

### 测试步骤1：初始状态验证
1. 打开GPSLogger应用
2. 打开侧边栏菜单（左滑或点击菜单按钮）
3. 观察"自动发送、发电子邮件和上传"部分下方的服务项
4. 点击主界面底部的上传按钮（向上箭头图标）
5. 观察弹出的上传菜单选项

**预期结果：**
- 如果没有启用任何自动发送服务，侧边栏应该只显示"自动发送、发电子邮件和上传"总设置项
- 上传菜单中应该只显示"立即自动发送"选项

### 测试步骤2：启用Dropbox自动发送
1. 点击侧边栏的"自动发送、发电子邮件和上传"
2. 找到并点击"Dropbox"选项
3. 开启"自动发送目标"开关
4. 返回主界面（按返回键）
5. 重新打开侧边栏菜单
6. 点击主界面底部的上传按钮

**预期结果：**
- 侧边栏应该显示"Dropbox"选项
- 上传菜单中应该显示"Dropbox"选项

### 测试步骤3：启用多个服务
1. 重复步骤2，分别启用以下服务：
   - Google云端硬盘
   - 电子邮件
   - SFTP
2. 每次启用一个服务后返回主界面验证

**预期结果：**
- 侧边栏应该显示所有已启用的服务
- 上传菜单中应该显示所有已启用的服务选项

### 测试步骤4：禁用服务验证
1. 进入设置，禁用Dropbox的"自动发送目标"开关
2. 返回主界面
3. 检查侧边栏和上传菜单

**预期结果：**
- Dropbox选项应该从侧边栏消失
- Dropbox选项应该从上传菜单消失
- 其他已启用的服务仍然显示

### 测试步骤5：全部禁用验证
1. 禁用所有服务的自动发送功能
2. 返回主界面
3. 检查菜单状态

**预期结果：**
- 侧边栏只显示"自动发送、发电子邮件和上传"总设置项
- 上传菜单只显示"立即自动发送"选项

## 验证要点

### 功能验证
- [ ] 侧边栏动态显示已启用的服务
- [ ] 主界面上传菜单动态显示已启用的服务
- [ ] 设置改变后菜单实时更新
- [ ] 未启用的服务不显示在菜单中

### 用户体验验证
- [ ] 菜单切换流畅，无卡顿
- [ ] 设置改变后立即生效
- [ ] 界面布局正常，无错位
- [ ] 图标和文字显示正确

### 边界情况验证
- [ ] 所有服务都未启用时的显示
- [ ] 所有服务都启用时的显示
- [ ] 快速切换启用/禁用状态
- [ ] 应用重启后设置保持

## 可能的问题和解决方案

### 问题1：菜单没有实时更新
**解决方案：** 手动重启应用或重新进入设置界面

### 问题2：某些服务选项仍然显示
**解决方案：** 检查该服务的自动发送设置是否真正关闭

### 问题3：应用崩溃
**解决方案：** 查看logcat日志，检查是否有空指针异常

## 技术实现说明

### 修改的文件
- `GpsMainActivity.java` - 主要实现文件

### 关键方法
- `addAutoSendDrawerItems()` - 动态添加侧边栏菜单项
- `refreshDrawerAutoSendItems()` - 刷新侧边栏菜单
- `updateUploadMenuItems()` - 更新主界面上传菜单
- `onResume()` - 添加了菜单刷新调用

### 判断逻辑
使用 `FileSenderFactory.getXXXSender().isAutoSendAvailable()` 方法判断服务是否应该显示在菜单中。

## 构建信息
- APK文件：`gpslogger/build/outputs/apk/debug/gpslogger-debug.apk`
- 构建时间：刚刚完成
- 构建状态：成功

请按照以上步骤进行测试，并记录任何异常情况。
